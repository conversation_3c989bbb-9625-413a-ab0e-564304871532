# ===========================================
# OpenAI 兼容 API 多密钥代理服务器配置文件 (Go版本)
# ===========================================

# ===========================================
# 服务器配置
# ===========================================
# 服务器端口
PORT=3000

# 服务器主机地址
HOST=0.0.0.0

# ===========================================
# 密钥管理配置
# ===========================================
# 密钥文件路径
KEYS_FILE=keys.txt

# 起始密钥索引
START_INDEX=0

# 黑名单阈值（错误多少次后拉黑密钥）
BLACKLIST_THRESHOLD=1

# 最大重试次数（换key重试）
MAX_RETRIES=3

# ===========================================
# OpenAI 兼容 API 配置
# ===========================================
# 上游 API 地址
OPENAI_BASE_URL=https://api.openai.com

# ===========================================
# 性能优化配置
# ===========================================
# 最大并发请求数
MAX_CONCURRENT_REQUESTS=100

# 启用 Gzip 压缩
ENABLE_GZIP=true

# ===========================================
# 日志配置
# ===========================================
# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info

# 日志格式 (text, json)
LOG_FORMAT=text

# 启用文件日志
LOG_ENABLE_FILE=false

# 日志文件路径
LOG_FILE_PATH=logs/app.log

# 启用请求日志（生产环境可设为 false 以提高性能）
LOG_ENABLE_REQUEST=true

# ===========================================
# 认证配置
# ===========================================
# 项目认证密钥（可选，如果设置则启用认证）
# AUTH_KEY=your-secret-key

# ===========================================
# CORS 配置
# ===========================================
# 是否启用 CORS
ENABLE_CORS=true

# 允许的来源（逗号分隔，* 表示允许所有）
ALLOWED_ORIGINS=*
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*
ALLOW_CREDENTIALS=false

# ===========================================
# 超时配置
# ===========================================
# 服务器读取超时时间（秒）
SERVER_READ_TIMEOUT=120

# 服务器写入超时时间（秒）
SERVER_WRITE_TIMEOUT=1800

# 服务器空闲超时时间（秒）
SERVER_IDLE_TIMEOUT=120

# 服务器优雅关闭超时时间（秒）
SERVER_GRACEFUL_SHUTDOWN_TIMEOUT=60

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# 响应超时时间（秒）- 控制TLS握手和响应头接收超时
RESPONSE_TIMEOUT=30

# 空闲连接超时时间（秒）- 控制连接池中空闲连接的生存时间
IDLE_CONN_TIMEOUT=120