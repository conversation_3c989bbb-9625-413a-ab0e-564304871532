#!/usr/bin/env python3
"""
测试脚本：诊断 Anthropic API 调用问题
"""

import requests
import json
import sys

def test_direct_api():
    """直接测试 Anthropic API 地址"""
    print("=== 直接测试 Anthropic API 地址 ===")
    
    base_url = "https://ampcode.com/api/provider/anthropic"
    
    # 测试不同的端点
    endpoints = [
        "/v1/messages",
        "/v1/chat/completions", 
        "/messages",
        "/chat/completions"
    ]
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n测试端点: {url}")
        
        try:
            # 发送 HEAD 请求检查端点是否存在
            response = requests.head(url, timeout=10)
            print(f"HEAD 请求状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 405:  # Method Not Allowed
                print("端点存在但不支持 HEAD 请求，尝试 OPTIONS")
                response = requests.options(url, timeout=10)
                print(f"OPTIONS 请求状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")

def test_with_auth():
    """使用认证测试 API"""
    print("\n=== 使用认证测试 API ===")
    
    # 注意：这里需要您提供真实的 API 密钥
    api_key = "your-anthropic-api-key-here"  # 请替换为真实密钥
    
    if api_key == "your-anthropic-api-key-here":
        print("请在脚本中设置真实的 API 密钥")
        return
    
    base_url = "https://ampcode.com/api/provider/anthropic"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"  # Anthropic API 版本
    }
    
    # 测试 Anthropic 格式的请求
    anthropic_payload = {
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ]
    }
    
    # 测试 OpenAI 格式的请求
    openai_payload = {
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user", 
                "content": "Hello, how are you?"
            }
        ]
    }
    
    test_cases = [
        ("/v1/messages", anthropic_payload, "Anthropic 格式"),
        ("/v1/chat/completions", openai_payload, "OpenAI 格式"),
    ]
    
    for endpoint, payload, description in test_cases:
        url = base_url + endpoint
        print(f"\n测试 {description}: {url}")
        
        try:
            response = requests.post(
                url, 
                headers=headers, 
                json=payload, 
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.text:
                try:
                    response_json = response.json()
                    print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                except:
                    print(f"响应内容 (文本): {response.text[:500]}")
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")

def test_url_construction():
    """测试 URL 构造逻辑"""
    print("\n=== 测试 URL 构造逻辑 ===")
    
    base_url = "https://ampcode.com/api/provider/anthropic"
    request_paths = [
        "/v1/chat/completions",
        "/v1/messages", 
        "/chat/completions",
        "/messages"
    ]
    
    print("模拟 gpt-load 的 URL 构造逻辑:")
    
    for path in request_paths:
        # 模拟 gpt-load 的逻辑
        if base_url.endswith("/"):
            final_url = base_url + path.lstrip("/")
        else:
            final_url = base_url + path
            
        print(f"请求路径: {path} -> 最终URL: {final_url}")

if __name__ == "__main__":
    print("Anthropic API 诊断工具")
    print("=" * 50)
    
    test_url_construction()
    test_direct_api()
    
    print("\n" + "=" * 50)
    print("注意事项:")
    print("1. 请确保您的 API 密钥是有效的")
    print("2. 检查 ampcode.com 的 API 文档了解正确的端点")
    print("3. 某些代理服务可能需要特定的请求头")
    print("4. 考虑在 .env 文件中调整 OPENAI_BASE_URL")
