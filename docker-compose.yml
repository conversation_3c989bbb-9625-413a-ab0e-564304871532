services:
  gpt-load:
    image: ghcr.io/tbphp/gpt-load:latest
    container_name: gpt-load
    ports:
      - "3000:3000"
    volumes:
      # 挂载密钥文件（只读）
      - ./keys.txt:/app/keys.txt:ro
    restart: unless-stopped
    env_file:
      - .env

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
