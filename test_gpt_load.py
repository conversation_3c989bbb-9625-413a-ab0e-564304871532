#!/usr/bin/env python3
"""
测试 gpt-load 代理服务器
"""

import requests
import json

def test_gpt_load():
    """测试 gpt-load 代理服务器"""
    print("=== 测试 gpt-load 代理服务器 ===")
    
    base_url = "http://localhost:3000"
    
    # 1. 测试健康检查
    print("\n1. 健康检查:")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"健康检查失败: {e}")
    
    # 2. 测试统计信息
    print("\n2. 统计信息:")
    try:
        response = requests.get(f"{base_url}/stats", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"统计信息获取失败: {e}")
    
    # 3. 测试黑名单
    print("\n3. 黑名单信息:")
    try:
        response = requests.get(f"{base_url}/blacklist", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"黑名单信息获取失败: {e}")
    
    # 4. 测试 API 调用（这会失败，但我们可以看到代理的行为）
    print("\n4. 测试 API 调用 (OpenAI 格式):")
    try:
        payload = {
            "model": "claude-3-sonnet-20240229",
            "messages": [
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "max_tokens": 100
        }
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容 (文本): {response.text}")
                
    except Exception as e:
        print(f"API 调用失败: {e}")
    
    # 5. 测试重置密钥
    print("\n5. 重置密钥:")
    try:
        response = requests.get(f"{base_url}/reset-keys", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"重置密钥失败: {e}")

if __name__ == "__main__":
    test_gpt_load()
