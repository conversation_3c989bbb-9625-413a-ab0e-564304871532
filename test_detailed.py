#!/usr/bin/env python3
"""
详细测试脚本：获取具体的错误信息
"""

import requests
import json

def test_post_request():
    """测试 POST 请求获取具体错误信息"""
    print("=== 测试 POST 请求获取错误信息 ===")
    
    base_url = "https://ampcode.com/api/provider/anthropic"
    
    # 测试不同的端点和请求格式
    test_cases = [
        {
            "endpoint": "/v1/chat/completions",
            "payload": {
                "model": "claude-3-sonnet-20240229",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 100
            },
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer test-key"
            },
            "description": "OpenAI 格式 - /v1/chat/completions"
        },
        {
            "endpoint": "/v1/messages", 
            "payload": {
                "model": "claude-3-sonnet-20240229",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 100
            },
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer test-key",
                "anthropic-version": "2023-06-01"
            },
            "description": "Anthropic 格式 - /v1/messages"
        }
    ]
    
    for test_case in test_cases:
        url = base_url + test_case["endpoint"]
        print(f"\n--- {test_case['description']} ---")
        print(f"URL: {url}")
        print(f"Headers: {test_case['headers']}")
        print(f"Payload: {json.dumps(test_case['payload'], indent=2)}")
        
        try:
            response = requests.post(
                url,
                headers=test_case["headers"],
                json=test_case["payload"],
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.text:
                try:
                    response_json = response.json()
                    print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                except:
                    print(f"响应内容 (文本): {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")

def test_options_request():
    """测试 OPTIONS 请求查看支持的方法"""
    print("\n=== 测试 OPTIONS 请求 ===")
    
    base_url = "https://ampcode.com/api/provider/anthropic"
    endpoints = ["/v1/chat/completions", "/v1/messages"]
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\nOPTIONS {url}")
        
        try:
            response = requests.options(url, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if 'Allow' in response.headers:
                print(f"允许的方法: {response.headers['Allow']}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")

if __name__ == "__main__":
    test_post_request()
    test_options_request()
