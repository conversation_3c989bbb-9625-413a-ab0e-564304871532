#!/usr/bin/env python3
"""
测试修改后的 gpt-load 对 Anthropic API 的支持
"""

import requests
import json

def test_anthropic_api():
    """测试修改后的 Anthropic API 支持"""
    print("=== 测试修改后的 gpt-load Anthropic API 支持 ===")
    
    base_url = "http://localhost:3000"
    
    # 1. 测试健康检查
    print("\n1. 健康检查:")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"健康检查失败: {e}")
    
    # 2. 测试正确的 Anthropic 格式请求
    print("\n2. 测试正确的 Anthropic 格式请求:")
    try:
        # 使用正确的 Anthropic 请求格式
        payload = {
            "model": "claude-sonnet-4-20250514",
            "max_tokens": 100,  # 必需字段
            "messages": [
                {
                    "role": "user",
                    "content": "讲个故事"
                }
            ]
        }
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",  # 使用 OpenAI 格式端点
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容 (文本): {response.text}")
                
    except Exception as e:
        print(f"API 调用失败: {e}")
    
    # 3. 测试直接使用 Anthropic 端点
    print("\n3. 测试直接使用 Anthropic 端点:")
    try:
        payload = {
            "model": "claude-sonnet-4-20250514",
            "max_tokens": 100,
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, how are you?"
                }
            ]
        }
        
        response = requests.post(
            f"{base_url}/v1/messages",  # 直接使用 Anthropic 端点
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容 (文本): {response.text}")
                
    except Exception as e:
        print(f"API 调用失败: {e}")
    
    # 4. 测试流式请求
    print("\n4. 测试流式请求:")
    try:
        payload = {
            "model": "claude-sonnet-4-20250514",
            "max_tokens": 50,
            "messages": [
                {
                    "role": "user",
                    "content": "Count from 1 to 5"
                }
            ],
            "stream": True
        }
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30,
            stream=True
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("流式响应内容:")
            for line in response.iter_lines():
                if line:
                    print(f"  {line.decode('utf-8')}")
        else:
            print(f"错误响应: {response.text}")
                
    except Exception as e:
        print(f"流式请求失败: {e}")

if __name__ == "__main__":
    test_anthropic_api()
